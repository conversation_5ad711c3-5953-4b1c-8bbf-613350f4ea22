"""
声学扫场测量模块

该模块提供声学扫场测量的核心功能，协同控制步进电机和数据采集系统，
实现空间中多点位的自动化声场信号采集。

主要功能：
1. 点阵管理：定义和管理二维空间中的测量点位
2. 扫场控制：自动控制机械臂移动到各个点位
3. 数据采集：在每个点位采集指定数量的声场信号chunk
4. 数据存储：规范化存储采集的数据，便于后续处理

核心类：
- Point2D: 二维空间点坐标的命名元组
- AcousticSweeper: 声学扫场测量控制器

使用示例：
    # 创建输出波形
    from sweeper400.analyze import init_sampling_info, init_sine_args, get_sine_cycles
    sampling_info = init_sampling_info(48000, 4800)
    sine_args = init_sine_args(1000.0, 1.0, 0.0)
    output_waveform = get_sine_cycles(sampling_info, sine_args, cycles=100)

    # 定义点阵
    grid = [
        Point2D(10.0, 20.0),
        Point2D(15.0, 20.0),
        Point2D(20.0, 20.0),
    ]

    # 创建扫场测量器（自动管理硬件控制器）
    sweeper = Sweeper(
        ai_channel="400Slot2/ai0",
        ao_channel="400Slot2/ao0",
        output_waveform=output_waveform,
        point_list=grid,
        chunks_per_point=6
    )

    # 非阻塞模式执行扫场测量（推荐）
    sweeper.sweep()  # 立即返回

    # 监控进度
    while sweeper.is_running():
        progress = sweeper.get_progress()
        print(f"进度: {progress['completed_points']}/{progress['total_points']}")
        time.sleep(1)



    # 保存数据
    sweeper.save_data("measurement_data.pkl")

    # 清理资源
    sweeper.cleanup()

线程化扫场的高级用法：
    # 设置进度回调
    def progress_callback(progress):
        print(f"当前点: {progress['current_point']}, "
              f"已完成: {progress['completed_points']}/{progress['total_points']}, "
              f"状态: {progress['state'].value}")

    sweeper.set_progress_callback(progress_callback)

    # 启动扫场
    sweeper.sweep()

    # 可以在扫场过程中执行其他操作
    time.sleep(5)

    # 检查状态和进度
    if sweeper.is_running():
        print(f"扫场进行中，完成度: {sweeper.get_completion_percentage():.1f}%")

    # 如果需要，可以中止扫场
    # sweeper.stop()

    # 等待完成
    success = sweeper.wait_for_completion()

注意事项：
- 使用前建议对步进电机进行零位校准
- 点阵中的坐标单位为毫米（mm）
- 采集过程中会自动等待电机停稳后再开始数据采集
- 新版本支持线程化扫场，可以在扫场过程中执行其他操作
- 使用stop()方法可以安全地中止正在进行的扫场
- 进度回调函数在扫场线程中执行，应避免耗时操作
"""

import time
import pickle
import threading
import numpy as np
from enum import Enum
from pathlib import Path
from typing import List, NamedTuple, TypedDict, Optional, Callable

from sweeper400.logger import get_logger
from sweeper400.measure import HiPerfCSSIO
from sweeper400.move import MotorController
from sweeper400.analyze import PositiveInt, PositiveFloat, Waveform

# 获取模块日志器
logger = get_logger(__name__)


# 定义扫场状态枚举
class SweeperState(Enum):
    """
    扫场测量状态枚举

    IDLE: 空闲状态，未开始扫场或已完成/中止
    RUNNING: 正在执行扫场测量
    STOPPING: 正在停止扫场测量
    COMPLETED: 扫场测量成功完成
    ERROR: 扫场测量出现错误
    """

    IDLE = "idle"
    RUNNING = "running"
    STOPPING = "stopping"
    COMPLETED = "completed"
    ERROR = "error"


# 定义进度信息类型
class SweeperProgress(TypedDict):
    """
    扫场进度信息

    current_point: 当前正在处理的点索引（从0开始）
    completed_points: 已完成的点数
    state: 当前扫场状态
    error_message: 错误信息（如果有）
    """

    current_point: int
    completed_points: int
    state: SweeperState
    error_message: Optional[str]


# 定义进度回调函数类型
ProgressCallback = Callable[[SweeperProgress], None]


# 定义二维空间点坐标的命名元组
class Point2D(NamedTuple):
    """
    二维空间点坐标

    Attributes:
        x: X轴坐标（mm）
        y: Y轴坐标（mm）
    """

    x: float
    y: float


# 辅助函数：以常见模式获取点阵（grid/point_list）
# 1. 矩形网格
def get_square_grid(
    x_start: float,
    x_end: float,
    x_points: PositiveInt,  # 可为1
    y_start: float,
    y_end: float,
    y_points: PositiveInt,  # 可为1
) -> List[Point2D]:
    """
    生成矩形网格点阵

    在指定的矩形区域内生成均匀分布的网格点阵。
    点的顺序为：先沿点数较少的轴扫描（相等则X轴优先），蛇形进行。

    Args:
        x_start: X轴起始坐标（mm）
        x_end: X轴结束坐标（mm）
        x_points: X轴方向的点数，可为1
        y_start: Y轴起始坐标（mm）
        y_end: Y轴结束坐标（mm）
        y_points: Y轴方向的点数，可为1

    Returns:
        List[Point2D]: 生成的点阵列表

    Raises:
        ValueError: 当参数无效时

    Examples:
        >>> # 生成3x3的网格，X轴从0到20mm，Y轴从0到20mm
        >>> grid = generate_grid_pattern(0, 20, 3, 0, 20, 3)
        >>> len(grid)
        9
    """
    if x_start > x_end or y_start > y_end:
        raise ValueError("起始坐标必须小于结束坐标")

    # 生成X和Y坐标数组
    x_coords = np.linspace(x_start, x_end, x_points)
    y_coords = np.linspace(y_start, y_end, y_points)

    # 生成网格点阵（点数较少的轴优先）
    grid: List[Point2D] = []
    if x_points <= y_points:
        for index, y in enumerate(y_coords):
            if index % 2 == 0:
                for x in x_coords:
                    grid.append(Point2D(float(x), float(y)))
            else:
                for x in reversed(x_coords):
                    grid.append(Point2D(float(x), float(y)))
    else:
        for index, x in enumerate(x_coords):
            if index % 2 == 0:
                for y in y_coords:
                    grid.append(Point2D(float(x), float(y)))
            else:
                for y in reversed(y_coords):
                    grid.append(Point2D(float(x), float(y)))

    logger.info(f"网格点阵生成完成，共 {len(grid)} 个点")

    return grid


# 2. 直线
def get_line_grid(
    x_start: float,
    y_start: float,
    x_end: float,
    y_end: float,
    num_points: PositiveInt,
) -> List[Point2D]:
    """
    生成直线点阵

    在两点之间生成均匀分布的直线点阵。

    Args:
        x_start: 起始点X坐标（mm）
        y_start: 起始点Y坐标（mm）
        x_end: 结束点X坐标（mm）
        y_end: 结束点Y坐标（mm）
        num_points: 点数

    Returns:
        List[Point2D]: 生成的点阵列表

    Raises:
        ValueError: 当参数无效时

    Examples:
        >>> # 生成从(0,0)到(100,0)的5个点
        >>> line = generate_line_pattern(0, 0, 100, 0, 5)
        >>> len(line)
        5
    """

    logger.info(
        f"生成直线点阵: 从({x_start}, {y_start})到({x_end}, {y_end}), {num_points}个点"
    )

    # 生成坐标数组
    x_coords = np.linspace(x_start, x_end, num_points)
    y_coords = np.linspace(y_start, y_end, num_points)

    # 生成点阵
    line: List[Point2D] = []
    for x, y in zip(x_coords, y_coords):
        line.append(Point2D(float(x), float(y)))

    logger.info(f"直线点阵生成完成，共 {len(line)} 个点")

    return line


# 定义Sweeper采集的单点原始数据格式
class PointRawData(TypedDict):
    """
    Sweeper采集的单点原始数据格式

    ## 内部组成:
        **position**: 测量点的二维坐标
        **ai_data**: 该点采集的所有AI波形
        **ao_data**: 该点对应的所有AO波形
    """

    position: Point2D
    ai_data: List[Waveform]
    ao_data: List[Waveform]


def load_measurement_data(file_path: str | Path) -> List[PointRawData]:
    """
    从文件加载测量数据

    加载由AcousticSweeper.save_data()保存的测量数据。

    Args:
        file_path: 数据文件的路径（.pkl文件）

    Returns:
        List[PointRawData]: 测量数据列表，每个元素包含：
            - "position": Point2D对象，表示该点的坐标
            - "ai_data": List[Waveform]，该点采集的所有AI波形
            - "ao_data": List[Waveform]，该点对应的所有AO波形

    Raises:
        FileNotFoundError: 当文件不存在时
        IOError: 当文件读取失败时

    Examples:
        >>> data = load_measurement_data("measurement_data.pkl")
        >>> print(f"加载了 {len(data)} 个点的数据")
        >>> first_point_position = data[0]["position"]
        >>> print(f"第一个点的坐标: ({first_point_position.x}, {first_point_position.y})")
    """
    file_path = Path(file_path)

    if not file_path.exists():
        raise FileNotFoundError(f"数据文件不存在: {file_path}")

    logger.info(f"开始加载测量数据: {file_path}")

    try:
        with open(file_path, "rb") as f:
            measurement_data = pickle.load(f)

        logger.info(f"数据加载成功，共 {len(measurement_data)} 个点")
        return measurement_data

    except Exception as e:
        logger.error(f"数据加载失败: {e}", exc_info=True)
        raise IOError(f"无法从 {file_path} 加载数据: {e}")


class Sweeper:
    """
    声学扫场测量控制器（线程化版本）

    该类协同控制步进电机和数据采集系统，实现自动化的声学扫场测量。
    在预定义的点阵中，依次移动到每个点位，采集指定数量的声场信号chunk。

    ## 线程化设计：
    - 扫场操作在后台线程中执行，主线程不被阻塞
    - 支持实时状态查询和进度监控
    - 可以随时中止正在进行的扫场操作
    - 提供进度回调机制，便于GUI集成

    ## 主要功能：
    1. 点阵管理：存储和验证测量点阵
    2. 运动控制：自动控制机械臂移动到各个点位
    3. 数据采集：在每个点位采集指定数量的chunk
    4. 数据存储：规范化存储所有点位的测量数据
    5. 状态监控：实时监控测量进度和状态
    6. 线程管理：后台执行扫场，支持中断和异常处理

    ## 使用方式：
    ```python
    # 创建输出波形
    from sweeper400.analyze import init_sampling_info, init_sine_args, get_sine_cycles
    sampling_info = init_sampling_info(48000, 4800)
    sine_args = init_sine_args(1000.0, 1.0, 0.0)
    output_waveform = get_sine_cycles(sampling_info, sine_args, cycles=100)

    # 创建扫场测量器（自动管理硬件控制器）
    sweeper = Sweeper(
        ai_channel="400Slot2/ai0",
        ao_channel="400Slot2/ao0",
        output_waveform=output_waveform,
        point_list=grid
    )

    # 非阻塞模式（推荐）
    sweeper.sweep()  # 立即返回
    while sweeper.is_running():
        progress = sweeper.get_progress()
        print(f"进度: {progress['completed_points']}/{progress['total_points']}")
        time.sleep(1)



    # 中止扫场
    sweeper.stop()

    # 清理资源（自动清理内部控制器）
    sweeper.cleanup()
    ```

    Attributes:
        ai_channel: AI通道名称
        ao_channel: AO通道名称
        output_waveform: 输出波形对象
        point_list: 测量点阵列表
        chunks_per_point: 每个点采集的chunk数量
        settle_time: 电机停止后的稳定等待时间（秒）
    """

    def __init__(
        self,
        ai_channel: str,
        ao_channel: str,
        output_waveform: Waveform,
        point_list: List[Point2D],
        chunks_per_point: PositiveInt = 3,
        settle_time: PositiveFloat = 0.5,
    ) -> None:
        """
        初始化声学扫场测量控制器

        Args:
            ai_channel: AI通道名称，例如 "400Slot2/ai0"
            ao_channel: AO通道名称，例如 "400Slot2/ao0"
            output_waveform: 输出波形对象
            point_list: 测量点阵，Point2D对象的列表
            chunks_per_point: 每个点采集的chunk数量，默认3
            settle_time: 电机停止后的稳定等待时间（秒），默认0.5秒

        Raises:
            ValueError: 当参数无效时
            RuntimeError: 当硬件初始化失败时
        """
        # 获取类日志器
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")

        # 验证参数
        if not point_list:
            logger.error("测量点阵不能为空", exc_info=True)
            raise ValueError("测量点阵不能为空")

        # 创建核心组件
        try:
            logger.info("正在初始化步进电机控制器...")
            self._move_controller = MotorController()
            logger.info("步进电机控制器初始化成功")
        except Exception as e:
            error_msg = f"步进电机控制器初始化失败: {e}"
            logger.error(error_msg, exc_info=True)
            raise RuntimeError(error_msg)

        try:
            logger.info("正在初始化数据采集控制器...")
            self._measure_controller = HiPerfCSSIO(
                ai_channel=ai_channel,
                ao_channel=ao_channel,
                output_waveform=output_waveform,
                export_function=self._data_export_callback,
            )
            logger.info("数据采集控制器初始化成功")
        except Exception as e:
            error_msg = f"数据采集控制器初始化失败: {e}"
            logger.error(error_msg, exc_info=True)
            # 清理已创建的电机控制器
            try:
                self._move_controller.cleanup()
            except Exception:
                pass
            raise RuntimeError(error_msg)

        # 存储测量参数
        self._point_list = point_list
        self._chunks_per_point = chunks_per_point
        self._settle_time = settle_time

        # 数据存储
        self._measurement_data: List[PointRawData] = []
        # 数据结构（列表索引即为点序号）:
        # [
        #     {
        #         "position": Point2D,
        #         "ai_data": List[Waveform],
        #         "ao_data": List[Waveform]
        #     },
        #     ...
        # ]

        # 状态标志和线程管理
        self._state = SweeperState.IDLE  # 扫场状态
        self._state_lock = threading.Lock()  # 保护状态的线程锁
        self._current_point_index = 0
        self._point_index_lock = threading.Lock()  # 保护点索引的线程锁
        self._enough_chunks_event = threading.Event()

        # 扫场工作线程
        self._sweep_thread: Optional[threading.Thread] = None
        self._sweep_complete_event = threading.Event()  # 扫场完成事件
        self._error_message: Optional[str] = None  # 错误信息

        # 进度回调
        self._progress_callback: Optional[ProgressCallback] = None

        logger.info(
            f"Sweeper 初始化完成 - "
            f"测量点数: {len(self._point_list)}, "
            f"每点chunk数: {self._chunks_per_point}, "
            f"稳定时间: {self._settle_time}s"
        )
        logger.debug(f"测量点阵: {self._point_list}")

    def _set_state(
        self, new_state: SweeperState, error_message: Optional[str] = None
    ) -> None:
        """
        线程安全地设置扫场状态

        Args:
            new_state: 新的状态
            error_message: 错误信息（仅在ERROR状态时使用）
        """
        with self._state_lock:
            old_state = self._state
            self._state = new_state
            if error_message is not None:
                self._error_message = error_message
            elif new_state != SweeperState.ERROR:
                self._error_message = None

        logger.debug(f"状态变更: {old_state.value} -> {new_state.value}")

        # 触发进度回调
        if self._progress_callback is not None:
            try:
                progress = self.get_progress()
                self._progress_callback(progress)
            except Exception as e:
                logger.warning(f"进度回调执行失败: {e}")

    def get_state(self) -> SweeperState:
        """
        获取当前扫场状态

        Returns:
            SweeperState: 当前状态
        """
        with self._state_lock:
            return self._state

    def get_progress(self) -> SweeperProgress:
        """
        获取扫场进度信息

        Returns:
            SweeperProgress: 进度信息字典
        """
        with self._state_lock:
            state = self._state
            error_message = self._error_message

        with self._point_index_lock:
            current_point = self._current_point_index

        return SweeperProgress(
            current_point=current_point,
            completed_points=current_point,  # 已完成的点数等于当前点索引
            state=state,
            error_message=error_message,
        )

    def set_progress_callback(self, callback: Optional[ProgressCallback]) -> None:
        """
        设置进度回调函数

        Args:
            callback: 进度回调函数，接收SweeperProgress参数。设为None可取消回调
        """
        self._progress_callback = callback
        logger.debug(f"进度回调已{'设置' if callback else '取消'}")

    def is_running(self) -> bool:
        """
        检查扫场是否正在运行

        Returns:
            bool: 是否正在运行
        """
        state = self.get_state()
        return state in (SweeperState.RUNNING, SweeperState.STOPPING)

    def _reset_sweep_state(self) -> None:
        """
        重置扫场相关状态
        """
        with self._point_index_lock:
            self._current_point_index = 0

        self._enough_chunks_event.clear()
        self._sweep_complete_event.clear()
        self._set_state(SweeperState.IDLE)

    def _data_export_callback(
        self, ai_waveform: Waveform, ao_waveform: Waveform, chunks_num: int
    ) -> None:
        """
        数据导出回调函数

        该函数会被HiPerfCSSIO在（后台工作线程中）每次数据导出时调用，用于收集测量数据。
        使用线程锁保证线程安全。

        Args:
            ai_waveform: 采集到的AI波形
            ao_waveform: 对应的AO波形
            chunks_num: 当前chunk编号（从1开始）
        """
        # 检查是否提前中止
        current_state = self.get_state()
        if current_state not in (SweeperState.RUNNING,):
            return

        # 使用线程锁安全地获取当前点的索引
        with self._point_index_lock:
            point_idx = self._current_point_index

        # 初始化当前点的数据存储（如果尚未初始化）
        # 由于点是按顺序处理的，point_idx 应该等于当前列表长度
        if point_idx >= len(self._measurement_data):
            current_position = self._point_list[point_idx]
            self._measurement_data.append(
                {
                    "position": current_position,
                    "ai_data": [],
                    "ao_data": [],
                }
            )
            logger.debug(f"初始化点 {point_idx} 的数据存储，位置: {current_position}")

        # 存储数据
        self._measurement_data[point_idx]["ai_data"].append(ai_waveform)
        self._measurement_data[point_idx]["ao_data"].append(ao_waveform)

        # 检查是否已采集足够chunk
        if chunks_num >= self._chunks_per_point:
            self._enough_chunks_event.set()
            # 触发进度回调（已完成点数通过current_point_index计算）
            if self._progress_callback is not None:
                try:
                    progress = self.get_progress()
                    self._progress_callback(progress)
                except Exception as e:
                    logger.warning(f"进度回调执行失败: {e}")

        logger.debug(
            f"点 {point_idx} 采集第 {chunks_num}/{self._chunks_per_point} 个chunk"
        )

    def _move_to_point(self, point: Point2D) -> bool:
        """
        移动到指定点位

        Args:
            point: 目标点位坐标

        Returns:
            bool: 移动是否成功
        """
        # 检查是否请求停止
        current_state = self.get_state()
        if current_state not in (SweeperState.RUNNING,):
            logger.debug("移动过程中检测到停止请求")
            return False

        logger.info(f"移动到点位: ({point.x:.3f}, {point.y:.3f}) mm")

        # 执行2D绝对运动
        success = self._move_controller.move_absolute_2D(x_mm=point.x, y_mm=point.y)

        if not success:
            logger.error(f"移动到点位 ({point.x:.3f}, {point.y:.3f}) 失败")
            return False

        # 等待电机稳定
        logger.debug(f"等待电机稳定 {self._settle_time}s")
        time.sleep(self._settle_time)

        return True

    def _collect_data_at_point(self, point_index: int) -> bool:
        """
        在当前点位采集数据。
        阻塞操作，会等待_enough_chunks_event事件到达。

        Args:
            point_index: 点位索引

        Returns:
            bool: 采集是否成功
        """
        logger.info(f"开始在点 {point_index} 采集数据...")

        # 预估持续时间并设置超时
        chunk_duration = self._measure_controller._output_waveform.duration  # type: ignore
        expected_duration = chunk_duration * self._chunks_per_point
        timeout = expected_duration * 2 + 5.0  # 设置超时为预期时间的2倍+5秒

        # 启用数据导出
        self._measure_controller.enable_export = True
        logger.debug("已启用数据导出")

        try:
            # 等待数据就绪事件或超时
            chunks_ready = self._enough_chunks_event.wait(timeout=timeout)

            # 事件成功到达
            if chunks_ready:
                # 停止数据导出
                self._measure_controller.enable_export = False
                # 清除事件标志
                self._enough_chunks_event.clear()
                logger.info(f"点 {point_index} 数据采集完成")
                return True

            # 事件超时
            else:
                self._measure_controller.enable_export = False
                logger.error(
                    f"点 {point_index} 数据采集超时（{timeout:.1f}s），强制停止"
                )
                return False

        except Exception as e:
            self._measure_controller.enable_export = False
            logger.error(f"点 {point_index} 数据采集过程中发生异常: {e}")
            return False

    def _sweep_worker_thread(self) -> None:
        """
        扫场工作线程的主函数

        在后台线程中执行扫场逻辑，支持中断和异常处理
        """
        try:
            logger.info("=" * 50)
            logger.info("开始扫场测量（后台线程）")
            logger.info(f"总测量点数: {len(self._point_list)}")
            logger.info(f"每点chunk数: {self._chunks_per_point}")
            logger.info("=" * 50)

            # 设置运行状态
            self._set_state(SweeperState.RUNNING)

            # 记录开始时间
            sweep_start_time = time.time()

            # 启动数据采集任务
            self._measure_controller.start()

            # 遍历所有测量点
            for point_index, point in enumerate(self._point_list):
                # 检查是否请求停止
                current_state = self.get_state()
                if current_state not in (SweeperState.RUNNING,):
                    logger.info("扫场测量已被中止")
                    self._set_state(SweeperState.IDLE)
                    return

                logger.info(
                    f"\n--- 处理点 {point_index + 1}/{len(self._point_list)} ---"
                )

                # 使用线程锁安全地更新当前点索引
                with self._point_index_lock:
                    self._current_point_index = point_index

                # 触发进度回调
                if self._progress_callback is not None:
                    try:
                        progress = self.get_progress()
                        self._progress_callback(progress)
                    except Exception as e:
                        logger.warning(f"进度回调执行失败: {e}")

                # 移动到目标点位
                if not self._move_to_point(point):
                    error_msg = f"移动到点 {point_index} 失败"
                    logger.error(error_msg)
                    self._set_state(SweeperState.ERROR, error_msg)
                    return

                # 在当前点位采集数据
                if not self._collect_data_at_point(point_index):
                    error_msg = f"点 {point_index} 数据采集失败"
                    logger.error(error_msg)
                    self._set_state(SweeperState.ERROR, error_msg)
                    return

            # 计算总耗时
            total_time = time.time() - sweep_start_time

            logger.info("=" * 50)
            logger.info("扫场测量完成！")
            logger.info(f"总测量点数: {len(self._point_list)}")
            logger.info(f"总耗时: {total_time:.2f}s ({total_time / 60:.2f}min)")
            logger.info(f"平均每点耗时: {total_time / len(self._point_list):.2f}s")
            logger.info("=" * 50)

            # 设置完成状态
            self._set_state(SweeperState.COMPLETED)

        except Exception as e:
            error_msg = f"扫场测量过程中发生异常: {e}"
            logger.error(error_msg, exc_info=True)
            self._set_state(SweeperState.ERROR, error_msg)

        finally:
            # 停止数据采集任务
            try:
                self._measure_controller.stop()
            except Exception as e:
                logger.warning(f"停止数据采集任务时出错: {e}")

            # 设置扫场完成事件
            self._sweep_complete_event.set()

    def sweep(self) -> bool:
        """
        启动扫场测量（非阻塞）

        在后台线程中执行扫场测量，立即返回。使用get_state()和get_progress()监控进度。

        Returns:
            bool: 是否成功启动扫场测量（不代表测量完成）

        Raises:
            RuntimeError: 当扫场已在运行或系统状态异常时
        """
        # 检查当前状态
        current_state = self.get_state()
        if current_state in (SweeperState.RUNNING, SweeperState.STOPPING):
            logger.warning("扫场测量已在运行中，无法重复启动")
            return False

        # 检查是否有之前的线程需要清理
        if self._sweep_thread is not None and self._sweep_thread.is_alive():
            logger.warning("检测到之前的扫场线程仍在运行，等待其结束...")
            self._sweep_thread.join(timeout=5.0)
            if self._sweep_thread.is_alive():
                logger.error("无法停止之前的扫场线程")
                return False

        try:
            # 重置状态
            self._reset_sweep_state()

            # 创建并启动扫场工作线程
            self._sweep_complete_event.clear()
            self._sweep_thread = threading.Thread(
                target=self._sweep_worker_thread,
                name="SweeperWorker",
                daemon=False,  # 不设为守护线程，确保能正常完成
            )
            self._sweep_thread.start()

            logger.info("扫场测量已启动（后台线程）")
            return True

        except Exception as e:
            error_msg = f"启动扫场测量失败: {e}"
            logger.error(error_msg, exc_info=True)
            self._set_state(SweeperState.ERROR, error_msg)
            return False

    def get_completion_percentage(self) -> float:
        """
        获取扫场完成百分比

        Returns:
            float: 完成百分比（0.0-100.0）
        """
        progress = self.get_progress()
        total_points = len(self._point_list)
        if total_points == 0:
            return 0.0
        return (progress["completed_points"] / total_points) * 100.0

    def stop(self, timeout: float = 10.0) -> bool:
        """
        中止扫场测量

        设置停止标志并等待扫场线程结束。

        Args:
            timeout: 等待超时时间（秒），默认10秒

        Returns:
            bool: 是否成功停止
        """
        current_state = self.get_state()

        if current_state == SweeperState.IDLE:
            logger.info("扫场测量未在运行")
            return True

        if current_state in (SweeperState.COMPLETED, SweeperState.ERROR):
            logger.info(f"扫场测量已结束，状态: {current_state.value}")
            return True

        logger.info("正在中止扫场测量...")

        # 设置停止状态
        self._set_state(SweeperState.STOPPING)

        # 中断数据采集等待
        self._enough_chunks_event.set()

        # 等待扫场线程结束
        if self._sweep_thread is not None and self._sweep_thread.is_alive():
            self._sweep_thread.join(timeout=timeout)

            if self._sweep_thread.is_alive():
                logger.warning(f"扫场线程未能在{timeout}秒内结束")
                return False

        logger.info("扫场测量已成功中止")
        self._set_state(SweeperState.IDLE)
        return True

    def get_data(self) -> List[PointRawData]:
        """
        获取测量数据

        Returns:
            List[PointRawData]: 测量数据列表，每个元素包含一个点的数据
        """
        return self._measurement_data

    def save_data(
        self,
        file_path: str | Path,
    ) -> None:
        """
        保存测量数据到文件

        使用pickle直接保存原始的_measurement_data列表，保证数据组织形式在整个生命周期中的一致性。

        数据结构：
        List[PointRawData]，每个PointRawData包含：
        - "position": Point2D对象，表示该点的坐标
        - "ai_data": List[Waveform]，该点采集的所有AI波形
        - "ao_data": List[Waveform]，该点对应的所有AO波形

        Args:
            file_path: 保存文件的路径（建议使用.pkl扩展名）

        Raises:
            ValueError: 当没有数据可保存时
            IOError: 当文件保存失败时
        """
        # python中，空容器视为False
        if not self._measurement_data:
            raise ValueError("没有可保存的数据，请先执行扫场测量")

        # 转换为Path对象
        file_path = Path(file_path)

        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # 使用pickle保存原始数据
        try:
            with open(file_path, "wb") as f:
                pickle.dump(self._measurement_data, f, protocol=pickle.HIGHEST_PROTOCOL)

            logger.info(f"数据保存成功: {file_path}")
            logger.info(f"文件大小: {file_path.stat().st_size / 1024 / 1024:.2f} MB")
            logger.info(f"包含 {len(self._measurement_data)} 个点的数据")
        except Exception as e:
            logger.error(f"数据保存失败: {e}", exc_info=True)
            raise IOError(f"无法保存数据到 {file_path}: {e}")

    def reset(self) -> None:
        """
        重置测量器状态

        清除所有已采集的数据，重置状态标志，准备进行新的测量。
        如果扫场正在运行，会先停止扫场。
        """
        logger.info("重置 Sweeper 状态")

        # 如果正在运行，先停止
        if self.is_running():
            logger.info("检测到扫场正在运行，先停止扫场...")
            self.stop(timeout=15.0)

        # 清除数据
        self._measurement_data.clear()

        # 重置状态标志（使用线程锁保护）
        self._reset_sweep_state()

        logger.info("Sweeper 状态已重置")

    def cleanup(self) -> None:
        """
        清理资源

        恢复原有的导出函数，清理状态，停止所有线程，销毁内部创建的控制器对象。
        """
        logger.info("清理 Sweeper 资源")

        # 如果正在运行，先停止
        if self.is_running():
            logger.info("检测到扫场正在运行，强制停止...")
            self.stop(timeout=15.0)

        # 清理数据采集控制器
        if hasattr(self, "_measure_controller"):
            try:
                logger.info("正在清理数据采集控制器...")
                self._measure_controller.stop()  # 确保停止任务
                # HiPerfCSSIO没有专门的cleanup方法，stop()已经处理了资源清理
                logger.info("数据采集控制器清理完成")
            except Exception as e:
                logger.warning(f"清理数据采集控制器时出错: {e}")

        # 清理步进电机控制器
        if hasattr(self, "_move_controller"):
            try:
                logger.info("正在清理步进电机控制器...")
                self._move_controller.cleanup()
                logger.info("步进电机控制器清理完成")
            except Exception as e:
                logger.warning(f"清理步进电机控制器时出错: {e}")

        # 重置状态
        try:
            self.reset()
        except Exception as e:
            logger.warning(f"重置状态时出错: {e}")

        logger.info("Sweeper 资源清理完成")

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        self.cleanup()
