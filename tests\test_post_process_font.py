"""
测试后处理模块的中文字体显示功能

这个测试脚本用于验证plot_transfer_function_spatial_distribution函数
是否能正确显示中文字符，以及日志输出是否已经精简。
"""

import matplotlib.pyplot as plt
import numpy as np
from sweeper400.analyze.post_process import plot_transfer_function_spatial_distribution
from sweeper400.logger import get_logger

# 获取测试日志器
logger = get_logger(__name__)


def test_chinese_font_display():
    """测试中文字体显示功能"""
    logger.info("开始测试中文字体显示功能")

    # 创建一个简单的测试图形
    fig, ax = plt.subplots(figsize=(8, 6))

    # 测试各种中文文本
    ax.set_title("传递函数 - 幅值比空间分布", fontsize=14, fontweight="bold")
    ax.set_xlabel("X 坐标 (mm)", fontsize=12)
    ax.set_ylabel("Y 坐标 (mm)", fontsize=12)

    # 添加一些测试数据
    x = np.linspace(0, 10, 10)
    y = np.linspace(0, 10, 10)
    X, Y = np.meshgrid(x, y)
    Z = np.sin(X) * np.cos(Y)

    scatter = ax.scatter(X.flatten(), Y.flatten(), c=Z.flatten(), cmap="viridis")
    cbar = fig.colorbar(scatter, ax=ax, label="幅值比")

    ax.grid(True, alpha=0.3, linestyle="--")
    ax.set_aspect("equal", adjustable="box")

    plt.tight_layout()

    # 保存测试图片
    test_save_path = "tests/test_chinese_font.png"
    fig.savefig(test_save_path, dpi=300, bbox_inches="tight")
    logger.info(f"测试图片已保存至: {test_save_path}")

    plt.close(fig)
    logger.info("中文字体显示测试完成")


def test_font_configuration():
    """测试字体配置"""
    logger.info("当前matplotlib字体配置:")
    logger.info(f"font.sans-serif: {plt.rcParams['font.sans-serif']}")
    logger.info(f"axes.unicode_minus: {plt.rcParams['axes.unicode_minus']}")

    # 测试可用字体
    from matplotlib import font_manager

    available_fonts = [f.name for f in font_manager.fontManager.ttflist]
    chinese_fonts = [
        f
        for f in available_fonts
        if any(
            keyword in f
            for keyword in ["SimHei", "Microsoft", "YaHei", "SimSun", "KaiTi"]
        )
    ]

    logger.info(f"系统中可用的中文字体: {chinese_fonts}")  # 只显示前10个


if __name__ == "__main__":
    logger.info("开始字体测试")

    # 测试字体配置
    test_font_configuration()

    # 测试中文字体显示
    test_chinese_font_display()

    logger.info("字体测试完成")
