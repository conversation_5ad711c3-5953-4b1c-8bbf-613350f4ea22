# pyright: basic
from sweeper400.use.sweeper import (
    Sweeper,
    get_square_grid,
)
from sweeper400.move import MotorController
from sweeper400.measure import HiPerfCSSI<PERSON>
from sweeper400.analyze import (
    init_sampling_info,
    init_sine_args,
    get_sine_cycles,
    Waveform,
    PositiveInt,
    calculate_transfer_function,
    plot_transfer_function_spatial_distribution,
)

# 创建输出波形
sampling_info = init_sampling_info(34300.0, 17150)  # 采样率34.3kHz, 0.5秒
sine_args = init_sine_args(
    frequency=3430.0, amplitude=0.05, phase=0.0
)  # 3430Hz正弦波，波长10cm
output_waveform = get_sine_cycles(sampling_info, sine_args)


# 创建一个简单的导出函数（这个会被Sweeper替换，但需要提供）
def dummy_export_function(
    ai_waveform: Waveform, ao_waveform: Waveform, chunks_num: PositiveInt
) -> None:
    pass


# 创建步进电机控制器和HiPerfCSSIO实例
mc1 = MotorController()
mc2 = HiPerfCSSIO(
    ai_channel="400Slot2/ai0",  # 传声器
    ao_channel="400Slot2/ao0",  # 扬声器
    output_waveform=output_waveform,
    export_function=dummy_export_function,
)

# 确定点阵
mc1.get_current_position_2D()
mc1.move_absolute_2D(120.0, 100.0)
# mc1.calibrate_all_axis()
grid = get_square_grid(120.0, 260.0, 15, 150.0, 250.0, 11)


sweeper = Sweeper(
    move_controller=mc1,
    measure_controller=mc2,
    point_list=grid,
    chunks_per_point=3,  # 每个点采集3个chunk
    settle_time=0.5,  # 稳定时间0.5秒
)

sweep_success = sweeper.sweep()
# sweeper.stop()

save_path = "D:\\EveryoneDownloaded\\test_data.pkl"
sweeper.save_data(save_path)

del sweeper, mc1, mc2
