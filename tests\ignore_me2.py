# pyright: basic
from sweeper400.use.sweeper import (
    load_measurement_data,
)
from sweeper400.analyze import (
    calculate_transfer_function,
    plot_transfer_function_spatial_distribution,
)

# save_path = "D:\\EveryoneDownloaded\\test_data.pkl"
save_path = "D:\\EveryoneDownloaded\\半场.pkl"
measurement_data = load_measurement_data(save_path)

# tf_results = calculate_transfer_function(measurement_data)
fig, (ax1, ax2) = plot_transfer_function_spatial_distribution(
    measurement_data, save_path="D:\\EveryoneDownloaded\\transfer_function.png"
)
